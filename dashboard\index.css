:root {
  --default-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Microsoft Yahei UI", "Microsoft Yahei",
    "Source Han Sans CN", sans-serif;
}

.main-container {
  overflow: hidden;
}

.main-container,
.main-container * {
  box-sizing: border-box;
}

input,
select,
textarea,
button {
  outline: 0;
}

.main-container {
  position: relative;
  width: 440px;
  height: 956px;
  margin: 0 auto;
  background: #67c16d;
  border: 1px solid #000000;
  overflow: hidden;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}
.bar-status {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: relative;
  width: 440px;
  height: 56px;
  margin: 0 0 0 0;
  padding: 15px 46px 15px 46px;
  background: #ffffff;
  z-index: 1;
  overflow: hidden;
}
.clock {
  flex-shrink: 0;
  position: relative;
  width: 54px;
  height: 20px;
  z-index: 2;
}
.clock-1 {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  width: 54px;
  height: 20px;
  top: 0;
  left: 0;
  color: #000000;
  font-family: Poppins, var(--default-font-family);
  font-size: 17px;
  font-weight: 600;
  line-height: 20px;
  text-align: center;
  white-space: nowrap;
  letter-spacing: -0.41px;
  z-index: 3;
}
.right-side {
  flex-shrink: 0;
  position: relative;
  width: 77.401px;
  height: 13px;
  z-index: 4;
}
.status-bar-battery {
  position: absolute;
  width: 27.401px;
  height: 13px;
  top: 0;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/ZW1ofCNdG2.png)
    no-repeat center;
  background-size: cover;
  transform: translate(41.24%, 0);
  z-index: 5;
}
.wifi {
  position: absolute;
  width: 21.96%;
  height: 91.03%;
  top: 7.69%;
  left: 33.59%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/x3oHySRXK2.png)
    no-repeat center;
  background-size: 100% 100%;
  z-index: 6;
}
.icon-mobile-signal {
  position: absolute;
  width: 18px;
  height: 12px;
  top: 1px;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/jmjGYQPM0m.png)
    no-repeat center;
  background-size: cover;
  transform: translate(-215%, 0);
  z-index: 7;
}
.flex-row-ad {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 157px;
  height: 30px;
  margin: 64px 0 0 35px;
  z-index: 11;
}
.ph-sun-bold {
  flex-shrink: 0;
  position: relative;
  width: 30px;
  height: 30px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/ije8Wz5JZr.png)
    no-repeat center;
  background-size: cover;
  z-index: 11;
  overflow: hidden;
}
.selamat-pagi {
  flex-shrink: 0;
  position: relative;
  height: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  text-align: left;
  white-space: nowrap;
  z-index: 10;
}
.flex-row-b {
  position: relative;
  width: 370px;
  height: 524.988px;
  margin: 117px 0 0 35px;
  z-index: 73;
  overflow: visible auto;
}
.hugeicons-waste {
  position: relative;
  width: 32px;
  height: 32px;
  margin: 238px 0 0 295px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/Q9iPJjeORT.png)
    no-repeat center;
  background-size: cover;
  z-index: 73;
  overflow: hidden;
}
.frame {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: nowrap;
  gap: 15px;
  position: absolute;
  width: 370px;
  height: 524.988px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 16;
}
.card {
  align-self: stretch;
  flex-shrink: 0;
  position: relative;
  min-width: 0;
  height: 177.988px;
  font-size: 0px;
  z-index: 17;
}
.balance {
  display: block;
  position: relative;
  height: 21.574px;
  margin: 66.88px 0 0 28.047px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 13px;
  font-weight: 500;
  line-height: 19.534px;
  text-align: left;
  white-space: nowrap;
  letter-spacing: -0.26px;
  z-index: 21;
}
.phone-number {
  display: block;
  position: relative;
  height: 21.574px;
  margin: 45.306px 0 0 28.047px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 13px;
  font-weight: 500;
  line-height: 19.534px;
  text-align: left;
  white-space: nowrap;
  letter-spacing: -0.39px;
  z-index: 23;
}
.rectangle {
  position: absolute;
  width: 370px;
  height: 177.988px;
  top: 0;
  left: 0;
  background: #c7ebc9;
  z-index: 18;
  border-radius: 14px;
}
.currency {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  height: 38px;
  top: 21.994px;
  left: 27.999px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 32px;
  font-weight: 600;
  line-height: 38px;
  text-align: left;
  white-space: nowrap;
  letter-spacing: -0.32px;
  z-index: 20;
}
.frame-2 {
  position: absolute;
  width: 32px;
  height: 32px;
  top: 27.994px;
  left: 290px;
  z-index: 19;
}
.waste-bank {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  height: 21.574px;
  top: 112px;
  left: 28px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 13px;
  font-weight: 600;
  line-height: 19.534px;
  text-align: left;
  white-space: nowrap;
  letter-spacing: -0.13px;
  z-index: 22;
}
.last-transaction {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  height: 21.574px;
  top: 117px;
  left: 276px;
  color: #0d2610;
  font-family: Poppins, var(--default-font-family);
  font-size: 13px;
  font-weight: 500;
  line-height: 19.534px;
  text-align: left;
  white-space: nowrap;
  letter-spacing: -0.39px;
  z-index: 24;
}
.frame-3 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 10px;
  position: relative;
  min-width: 0;
  height: 140px;
  padding: 30px 25px 30px 25px;
  background: #ffffff;
  z-index: 25;
  border-radius: 10px;
  box-shadow: 0 5px 4px 0 rgba(0, 0, 0, 0.25);
}
.frame-4 {
  display: flex;
  align-items: center;
  align-self: stretch;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 15px;
  position: relative;
  height: 79px;
  z-index: 26;
}
.frame-5 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 5px;
  position: relative;
  width: 236px;
  z-index: 27;
}
.monthly-waste-deposit {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  width: 236px;
  height: 21px;
  color: #1e1e1e;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
  text-overflow: initial;
  white-space: nowrap;
  z-index: 28;
  overflow: hidden;
}
.kg {
  flex-shrink: 0;
  position: relative;
  width: 76px;
  height: 27px;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
  text-overflow: initial;
  white-space: nowrap;
  z-index: 29;
}
.kg-value {
  position: relative;
  color: #1e1e1e;
  font-family: Poppins, var(--default-font-family);
  font-size: 18px;
  font-weight: 500;
  line-height: 21.6px;
  text-align: left;
}
.space {
  position: relative;
  color: #1e1e1e;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
}
.kg-6 {
  position: relative;
  color: #1e1e1e;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
}
.frame-7 {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 5px;
  position: relative;
  width: 122px;
  z-index: 30;
}
.frame-8 {
  flex-shrink: 0;
  position: relative;
  width: 17px;
  height: 17px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/TjEVFJvh98.png)
    no-repeat center;
  background-size: cover;
  z-index: 31;
  overflow: hidden;
}
.kali-setoran {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 21px;
  color: #1e1e1e;
  font-family: Poppins, var(--default-font-family);
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
  white-space: nowrap;
  z-index: 32;
}
.group {
  flex-shrink: 0;
  position: relative;
  width: 70px;
  height: 70px;
  z-index: 33;
}
.frame-9 {
  position: absolute;
  width: 70px;
  height: 70px;
  top: 0;
  left: 0;
  font-size: 0px;
  z-index: 34;
  overflow: hidden;
}
.setor {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 50px;
  height: 21px;
  margin: 41.512px 0 0 10px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  text-align: center;
  white-space: nowrap;
  z-index: 36;
}
.group-a {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/SKHabPZNf5.png)
    no-repeat center;
  background-size: 100% 100%;
  z-index: 35;
  border-radius: 10px;
}
.promo {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  flex-shrink: 0;
  gap: 50px;
  position: relative;
  width: 1228px;
  z-index: 37;
  overflow: auto hidden;
}
.group-b {
  flex-shrink: 0;
  position: relative;
  width: 376px;
  height: 177px;
  z-index: 38;
}
.rectangle-c {
  position: absolute;
  width: 376px;
  height: 177px;
  top: 0;
  left: 0;
  background: #67c16d;
  z-index: 39;
  border-radius: 40px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}
.pic-4 {
  position: absolute;
  width: 135px;
  height: 135px;
  top: 20px;
  left: 241px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/fqp3PLGS57.png)
    no-repeat center;
  background-size: cover;
  z-index: 40;
  overflow: hidden;
}
.ayo-jaga-kebersihan {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 247px;
  height: 68px;
  top: 20px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
  z-index: 41;
}
.jaga-kebersihan-lorem-ipsum {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 232px;
  height: 55px;
  top: 98px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: left;
  z-index: 42;
}
.group-d {
  flex-shrink: 0;
  position: relative;
  width: 376px;
  height: 177px;
  z-index: 43;
}
.rectangle-e {
  position: absolute;
  width: 376px;
  height: 177px;
  top: 0;
  left: 0;
  background: #67c16d;
  z-index: 44;
  border-radius: 40px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}
.img-5 {
  position: absolute;
  width: 135px;
  height: 135px;
  top: 20px;
  left: 241px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/hftM1gDFfW.png)
    no-repeat center;
  background-size: cover;
  z-index: 45;
  overflow: hidden;
}
.ayo-jaga-kebersihan-f {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 247px;
  height: 68px;
  top: 20px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
  z-index: 46;
}
.kebersihan-sebagian-dari-iman {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 232px;
  height: 55px;
  top: 98px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: left;
  z-index: 47;
}
.group-10 {
  flex-shrink: 0;
  position: relative;
  width: 376px;
  height: 177px;
  z-index: 48;
}
.rectangle-11 {
  position: absolute;
  width: 376px;
  height: 177px;
  top: 0;
  left: 0;
  background: #67c16d;
  z-index: 49;
  border-radius: 40px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
}
.empty {
  position: absolute;
  width: 135px;
  height: 135px;
  top: 20px;
  left: 241px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/5Wo3oH1ed6.png)
    no-repeat center;
  background-size: cover;
  z-index: 50;
  overflow: hidden;
}
.ayo-jaga-kebersihan-12 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 247px;
  height: 68px;
  top: 20px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  text-align: left;
  z-index: 51;
}
.kebersihan-sebagian-iman {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 232px;
  height: 55px;
  top: 98px;
  left: 27px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: left;
  z-index: 52;
}
.flex-row-e {
  position: relative;
  width: 469px;
  height: 103px;
  margin: 65.012px 0 0 -15px;
  z-index: 62;
}
.ellipse {
  position: absolute;
  width: 13.43%;
  height: 61.17%;
  top: 0;
  left: 41.79%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/mAiFFYXYBM.png)
    no-repeat center;
  background-size: 100% 100%;
  z-index: 62;
  border-radius: 50%;
}
.rectangle-13 {
  position: absolute;
  width: 469px;
  height: 88px;
  top: 15px;
  left: 0;
  background: #ffffff;
  z-index: 54;
  box-shadow: 0 -5px 5px 0 rgba(0, 0, 0, 0.1);
}
.hugeicons-waste-14 {
  position: absolute;
  width: 32px;
  height: 32px;
  top: 50%;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/9i8QEAPDcr.png)
    no-repeat center;
  background-size: cover;
  transform: translate(-70.31%, -140.63%);
  z-index: 64;
  overflow: hidden;
}
.rectangle-15 {
  position: absolute;
  width: 48px;
  height: 5px;
  top: 0;
  left: 36px;
  background: #67c16d;
  z-index: 72;
  border-radius: 15px;
}
.frame-16 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: absolute;
  width: 11.94%;
  height: 71.59%;
  top: 12.64%;
  left: 6.88%;
  padding: 5px 10px 5px 10px;
  z-index: 55;
}
.home {
  flex-shrink: 0;
  position: relative;
  width: 30px;
  height: 30px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/u0nSOsBJ3m.png)
    no-repeat center;
  background-size: cover;
  z-index: 56;
}
.home-17 {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 18px;
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  text-align: left;
  white-space: nowrap;
  z-index: 57;
}
.frame-18 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: absolute;
  width: 62px;
  height: 60px;
  top: 50%;
  left: 50%;
  padding: 5px 10px 5px 10px;
  transform: translate(71.77%, -54.17%);
  z-index: 66;
}
.icon-park-outline-transaction-order {
  flex-shrink: 0;
  position: relative;
  width: 27px;
  height: 27px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/8JecezFjeD.png)
    no-repeat center;
  background-size: cover;
  z-index: 67;
  overflow: hidden;
}
.history {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 18px;
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  text-align: left;
  white-space: nowrap;
  z-index: 68;
}
.frame-19 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: absolute;
  width: 12.37%;
  height: 71.59%;
  top: 13.89%;
  left: 79.56%;
  padding: 5px 10px 5px 10px;
  z-index: 69;
}
.user-circle {
  flex-shrink: 0;
  position: relative;
  width: 30px;
  height: 30px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/HurMX4QORO.png)
    no-repeat center;
  background-size: cover;
  z-index: 70;
}
.profile {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 18px;
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  text-align: left;
  white-space: nowrap;
  z-index: 71;
}
.frame-1a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  position: absolute;
  width: 61px;
  height: 56px;
  top: 50%;
  left: 50%;
  padding: 0 10px 0 10px;
  transform: translate(-194.26%, -53.57%);
  z-index: 58;
}
.moneybag {
  flex-shrink: 0;
  position: relative;
  width: 31px;
  height: 31px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/rskd8qdqso.png)
    no-repeat center;
  background-size: cover;
  z-index: 59;
  overflow: hidden;
}
.reward {
  flex-shrink: 0;
  flex-basis: auto;
  position: relative;
  height: 18px;
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  text-align: left;
  white-space: nowrap;
  z-index: 60;
}
.setor-1b {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  width: 42px;
  height: 24px;
  top: 50px;
  left: calc(50% - 27.5px);
  color: #67c16d;
  font-family: Poppins, var(--default-font-family);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
  white-space: nowrap;
  z-index: 65;
}
.head {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: absolute;
  width: 211px;
  height: 36px;
  top: 148px;
  left: 34px;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 24px;
  font-weight: 600;
  line-height: 36px;
  text-align: center;
  white-space: nowrap;
  z-index: 14;
}
.flex-row {
  position: absolute;
  width: 370px;
  height: 82px;
  top: 148px;
  left: 34px;
  z-index: 15;
}
.screenshot {
  position: absolute;
  width: 76px;
  height: 76px;
  top: 0;
  left: 294px;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/rKF2ke4Nao.png)
    no-repeat center;
  background-size: cover;
  z-index: 15;
  border-radius: 50%;
}
.desc {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  position: absolute;
  width: 238px;
  height: 46px;
  top: 36px;
  left: 0;
  color: #ffffff;
  font-family: Poppins, var(--default-font-family);
  font-size: 15px;
  font-weight: 500;
  line-height: 22.5px;
  text-align: left;
  z-index: 13;
}
.rectangle-1c {
  position: absolute;
  width: 440px;
  height: 662px;
  top: 294px;
  left: 50%;
  background: url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-07-10/ijEoF1JWww.png)
    no-repeat center;
  background-size: cover;
  transform: translate(-50.23%, 0);
}
